from fastapi import APIRouter, HTTPException
from typing import Optional
from ..models import Technician
from ..crud import (
    get_technician_by_id, update_technician_by_id, delete_technician_by_id
)

router = APIRouter()

@router.get("/technicians/{technician_id}", response_model=Technician)
def read_technician(technician_id: str):
    tech = get_technician_by_id(technician_id)
    if not tech:
        raise HTTPException(status_code=404, detail="Technician not found")
    return tech

@router.put("/technicians/{technician_id}", response_model=Technician)
def update_technician(technician_id: str, name: Optional[str] = None, email: Optional[str] = None, skills: Optional[list] = None, role: Optional[str] = None):
    updated = update_technician_by_id(technician_id, name=name, email=email, skills=skills, role=role)
    if not updated:
        raise HTTPException(status_code=400, detail="No fields to update or update failed")
    tech = get_technician_by_id(technician_id)
    if not tech:
        raise HTTPException(status_code=404, detail="Technician not found after update")
    return tech

@router.delete("/technicians/{technician_id}")
def delete_technician(technician_id: str):
    deleted = delete_technician_by_id(technician_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Technician not found or could not be deleted")
    return {"detail": "Technician deleted"} 