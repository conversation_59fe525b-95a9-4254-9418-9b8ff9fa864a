from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional
from datetime import datetime

class Ticket(BaseModel):
    ticket_number: str = Field(..., alias="TICKETNUMBER")
    issue_type: str
    sub_issue_type: str
    ticket_category: str
    priority: str
    description: str
    requester_name: str
    requester_email: EmailStr
    due_date: str  # ISO format string
    status: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class Technician(BaseModel):
    technician_id: str
    name: str
    email: EmailStr
    skills: List[str]
    role: str
    availability_status: Optional[str] = None
    current_workload: Optional[int] = None
    max_workload: Optional[int] = None
    specializations: Optional[List[str]] = None 