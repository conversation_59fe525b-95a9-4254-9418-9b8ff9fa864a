from .database import execute_query
from typing import List, Dict, Optional

# --- Ticket CRUD ---
def get_all_tickets() -> List[Dict]:
    query = """
    SELECT * FROM TEST_DB.PUBLIC.COMPANY_4130_DATA
    ORDER BY CREATEDATE DESC
    LIMIT 100
    """
    return execute_query(query)

def get_ticket_by_number(ticket_number: str) -> Optional[Dict]:
    query = """
    SELECT * FROM TEST_DB.PUBLIC.COMPANY_4130_DATA WHERE TICKETNUMBER = %s
    """
    results = execute_query(query, (ticket_number,))
    return results[0] if results else None

def update_ticket_by_number(ticket_number: str, status: Optional[str] = None, priority: Optional[str] = None) -> bool:
    set_clauses = []
    params = []
    if status:
        set_clauses.append("STATUS = %s")
        params.append(status)
    if priority:
        set_clauses.append("PRIORITY = %s")
        params.append(priority)
    if not set_clauses:
        return False
    params.append(ticket_number)
    query = f"""
    UPDATE TEST_DB.PUBLIC.COMPANY_4130_DATA
    SET {', '.join(set_clauses)}
    WHERE TICKETNUMBER = %s
    """
    execute_query(query, tuple(params))
    return True

def delete_ticket_by_number(ticket_number: str) -> bool:
    query = """
    DELETE FROM TEST_DB.PUBLIC.COMPANY_4130_DATA WHERE TICKETNUMBER = %s
    """
    execute_query(query, (ticket_number,))
    return True

# --- Technician CRUD ---
def get_technician_by_id(technician_id: str) -> Optional[Dict]:
    query = """
    SELECT * FROM TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA WHERE TECHNICIAN_ID = %s
    """
    results = execute_query(query, (technician_id,))
    return results[0] if results else None

def update_technician_by_id(technician_id: str, name: Optional[str] = None, email: Optional[str] = None, skills: Optional[List[str]] = None, role: Optional[str] = None) -> bool:
    set_clauses = []
    params = []
    if name:
        set_clauses.append("NAME = %s")
        params.append(name)
    if email:
        set_clauses.append("EMAIL = %s")
        params.append(email)
    if skills is not None:
        set_clauses.append("SKILLS = %s")
        params.append(",".join(skills))
    if role:
        set_clauses.append("ROLE = %s")
        params.append(role)
    if not set_clauses:
        return False
    params.append(technician_id)
    query = f"""
    UPDATE TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA
    SET {', '.join(set_clauses)}
    WHERE TECHNICIAN_ID = %s
    """
    execute_query(query, tuple(params))
    return True

def delete_technician_by_id(technician_id: str) -> bool:
    query = """
    DELETE FROM TEST_DB.PUBLIC.TECHNICIAN_DUMMY_DATA WHERE TECHNICIAN_ID = %s
    """
    execute_query(query, (technician_id,))
    return True 